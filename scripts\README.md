# Scripts Documentation

This directory contains various database migration and utility scripts for the Quartermaster application.

## addUnitsToVesselManagement.js

This script migrates units from the vessel service to the vessel management database in quartermaster-shared.

### Purpose

The script performs the following operations:
1. Fetches all unit IDs from the vessel service (similar to `/unitIds` API)
2. Checks for existing vessel records to avoid duplicates
3. For each unit, looks for location data in `{unit_id}_location` collections
4. Creates vessel records in the quartermaster-shared database with:
   - Unit name and ID
   - Active status (false for units named "Unregistered")
   - Location history if available
   - System user as creator

### Usage

#### Dry Run (Recommended First)
```bash
node scripts/addUnitsToVesselManagement.js --dry-run
```

#### Live Run
```bash
node scripts/addUnitsToVesselManagement.js
```

### Prerequisites

1. **Environment Variables**: Ensure your `.env` file is properly configured with:
   - `MONGO_URI`: MongoDB connection string

2. **System User**: The script requires at least one user with `role_id: 1` (admin) in the database, or a user with:
   - Email: `<EMAIL>`, OR
   - Username: `system`

3. **Database Access**: The script needs access to both:
   - `quartermaster` database (for unit location data)
   - `quartermaster-shared` database (for vessel management)

### Features

- **Dry Run Mode**: Use `--dry-run` flag to preview changes without making modifications
- **Batch Processing**: Processes units in batches of 10 to avoid overwhelming the database
- **Duplicate Prevention**: Checks for existing vessels before creating new ones
- **Location Data Integration**: Attempts to find coordinates from unit location collections
- **Comprehensive Logging**: Detailed progress and summary reporting
- **Transaction Safety**: Uses database transactions for data integrity
- **Error Handling**: Continues processing even if individual units fail

### Output

The script provides detailed logging including:
- Progress updates for each batch and unit
- Location data discovery status
- Summary statistics:
  - Total units processed
  - Successfully created vessels
  - Skipped duplicates
  - Units with/without location data
  - Error count

### Example Output

```
=== Vessel Management Migration Script ===
Mode: DRY RUN (no changes will be made)
Starting vessel management migration...

Step 1: Finding system user...
Using system user: System Admin (507f1f77bcf86cd799439011)

Step 2: Fetching all units from vessel service...
Found 150 units

Step 3: Checking existing vessels...
Found 25 existing vessels in vessel management

Step 4: Processing units...
Processing batch 1/15 (10 units)
  Processing unit: QMTB000001 (Vessel Alpha)
    - Found location data: 40.7128, -74.0060
    - [DRY RUN] Would create vessel with data: {...}
  Processing unit: QMTB000002 (Unregistered)
    - No location data found for QMTB000002
    - [DRY RUN] Would create vessel with data: {...}

=== Migration Summary ===
Mode: DRY RUN
Total units found: 150
Successfully processed: 125
Skipped (already exists): 25
Units with location data: 89
Units without location data: 36
Errors: 0

This was a dry run. No changes were made to the database.
Run without --dry-run flag to execute the migration.
```

### Troubleshooting

1. **"No system user found"**: Create an admin user or system user in the database
2. **Database connection errors**: Check your `MONGO_URI` environment variable
3. **Permission errors**: Ensure the database user has read/write access to both databases
4. **Memory issues**: The script processes in batches, but for very large datasets, consider adjusting `BATCH_SIZE`

### Notes

- Units with name "Unregistered" are marked as inactive (`is_active: false`)
- The script uses the vessel schema's pre-save hook to automatically populate `units_history`
- Location collections follow the pattern `{unit_id}_location`
- The script is idempotent - running it multiple times won't create duplicates
