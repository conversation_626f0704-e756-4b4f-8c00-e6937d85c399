require("dotenv").config();
const db = require("../modules/db");
const Vessel = require("../models/VesselManagement");
const User = require("../models/User");
const vesselService = require("../services/Vessel.service");

// Configuration
const DRY_RUN = process.argv.includes('--dry-run');
const BATCH_SIZE = 10; // Process units in batches to avoid overwhelming the database

async function addUnitsToVesselManagement() {
    const session = await db.qmShared.startSession();

    try {
        console.log("=== Vessel Management Migration Script ===");
        console.log(`Mode: ${DRY_RUN ? 'DRY RUN (no changes will be made)' : 'LIVE RUN'}`);
        console.log("Starting vessel management migration...\n");

        // Step 1: Get a system user for created_by field
        console.log("Step 1: Finding system user...");
        const createdByUser = await User.findOne({
            _id: "66942a54a7f848634a00990a"
        });

        console.log(`Using system user: ${createdByUser.name} (${createdByUser._id})`);

        // Step 2: Fetch all units from vessel service
        console.log("\nStep 2: Fetching all units from vessel service...");
        const allUnits = await vesselService.fetchAll();
        console.log(`Found ${allUnits.length} units\n`);

        // Step 3: Check existing vessels
        console.log("Step 3: Checking existing vessels...");
        const existingVessels = await Vessel.find({}, { unit_id: 1 });
        const existingUnitIds = new Set(existingVessels.map(v => v.unit_id).filter(Boolean));
        console.log(`Found ${existingVessels.length} existing vessels in vessel management\n`);

        // Step 4: Process each unit
        console.log("Step 4: Processing units...");
        let processedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;
        let unitsWithLocation = 0;
        let unitsWithoutLocation = 0;

        // Process units in batches
        for (let i = 0; i < allUnits.length; i += BATCH_SIZE) {
            const batch = allUnits.slice(i, i + BATCH_SIZE);
            console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(allUnits.length / BATCH_SIZE)} (${batch.length} units)`);

            if (!DRY_RUN) {
                session.startTransaction();
            }

            for (const unit of batch) {
                try {
                    const unitId = unit.unit_id;
                    const unitName = unit.name;

                    console.log(`  Processing unit: ${unitId} (${unitName})`);

                    // Check if vessel already exists using our pre-fetched set
                    if (existingUnitIds.has(unitId)) {
                        console.log(`    - Vessel already exists, skipping`);
                        skippedCount++;
                        continue;
                    }

                    // Determine if unit is active (not "Unregistered")
                    const isActive = unitName !== "Unregistered";

                    // Check for location collection and get coordinates
                    let locationData = null;
                    try {
                        const locationCollection = db.qm.collection(`${unitId}_location`);

                        // Try to get the first location record with coordinates
                        const firstLocation = await locationCollection.findOne(
                            {
                                latitude: { $exists: true, $ne: null },
                                longitude: { $exists: true, $ne: null }
                            },
                            {
                                projection: { latitude: 1, longitude: 1, timestamp: 1 },
                                sort: { timestamp: 1 }
                            }
                        );

                        if (firstLocation) {
                            locationData = {
                                latitude: firstLocation.latitude,
                                longitude: firstLocation.longitude,
                                timestamp: firstLocation.timestamp
                            };
                            console.log(`    - Found location data: ${locationData.latitude}, ${locationData.longitude}`);
                            unitsWithLocation++;
                        } else {
                            console.log(`    - No location data found for ${unitId}`);
                            unitsWithoutLocation++;
                        }
                    } catch (locationError) {
                        console.log(`    - Location collection ${unitId}_location does not exist or error accessing it`);
                        unitsWithoutLocation++;
                    }

                    // Create vessel record
                    const vesselData = {
                        name: unitName,
                        unit_id: unitId,
                        is_active: isActive,
                        created_by: createdByUser._id,
                        creation_timestamp: new Date()
                    };

                    if (DRY_RUN) {
                        console.log(`    - [DRY RUN] Would create vessel with data:`, vesselData);
                    } else if (isActive) {
                        const vessel = await Vessel.create(vesselData);
                        console.log(`    - Created vessel: ${vessel._id}`);
                    }
                    processedCount++;

                } catch (unitError) {
                    console.error(`    Error processing unit ${unit.unit_id}:`, unitError.message);
                    errorCount++;
                }
            }

            if (!DRY_RUN) {
                await session.commitTransaction();
                console.log(`  Batch completed successfully\n`);
            }
        }

        console.log("\n=== Migration Summary ===");
        console.log(`Mode: ${DRY_RUN ? 'DRY RUN' : 'LIVE RUN'}`);
        console.log(`Total units found: ${allUnits.length}`);
        console.log(`Successfully processed: ${processedCount}`);
        console.log(`Skipped (already exists): ${skippedCount}`);
        console.log(`Units with location data: ${unitsWithLocation}`);
        console.log(`Units without location data: ${unitsWithoutLocation}`);
        console.log(`Errors: ${errorCount}`);

        if (DRY_RUN) {
            console.log("\nThis was a dry run. No changes were made to the database.");
            console.log("Run without --dry-run flag to execute the migration.");
        } else {
            console.log("\nMigration completed successfully!");
        }

    } catch (error) {
        if (!DRY_RUN) {
            try {
                await session.abortTransaction();
            } catch (abortError) {
                console.error("Error aborting transaction:", abortError);
            }
        }
        console.error("Migration failed:", error);
        throw error;
    } finally {
        try {
            session.endSession();
        } catch (sessionError) {
            console.error("Error ending session:", sessionError);
        }
        process.exit(0);
    }
}

// Run the migration
addUnitsToVesselManagement().catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
});
